@import "tailwindcss";

@theme {
  --color-background: #f8f9fa;
  --color-background-secondary: #e9ecef;
  --color-primary: #0084ff;
  --color-primary-dark: #005fa3;
  --color-accent: #00baff;
  --color-text-primary: #212529;
  --color-text-secondary: #6c757d;
  --color-border: #dee2e6;
  --color-button-background: #0084ff;
  --color-button-text: #ffffff;
  --color-input-background: #ffffff;
  --color-input-border: #ced4da;
  --color-input-text: #212529;
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

:root {
  /* Light mode colors */
  --background: #f8f9fa;
  --background-secondary: #e9ecef;
  --primary: #0084ff;
  --primary-dark: #005fa3;
  --accent: #00baff;
  --text-primary: #212529;
  --text-secondary: #6c757d;
  --border: #dee2e6;
  --button-background: #0084ff;
  --button-text: #ffffff;
  --input-background: #ffffff;
  --input-border: #ced4da;
  --input-text: #212529;
}

[data-theme="dark"] {
  /* Dark mode colors */
  --background: #121417;
  --background-secondary: #1e1f21;
  --primary: #00baff;
  --primary-dark: #0084ff;
  --accent: #4dcfff;
  --text-primary: #f8f9fa;
  --text-secondary: #adb5bd;
  --border: #2b2f33;
  --button-background: #0084ff;
  --button-text: #ffffff;
  --input-background: #1e1f21;
  --input-border: #495057;
  --input-text: #f8f9fa;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--text-primary);
  font-family: var(--font-geist-sans), Arial, Helvetica, sans-serif;
  line-height: 1.6;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Wave animation keyframes */
@keyframes wave {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes wave-reverse {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(10px);
  }
}

.wave-animation {
  animation: wave 3s ease-in-out infinite;
}

.wave-animation-reverse {
  animation: wave-reverse 3s ease-in-out infinite;
  animation-delay: 1.5s;
}

/* Smooth transitions for interactive elements */
.transition-smooth {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-dark);
}

/* Custom utility classes for theme colors */
.bg-background {
  background-color: var(--background);
}

.bg-background-secondary {
  background-color: var(--background-secondary);
}

.bg-primary {
  background-color: var(--primary);
}

.bg-primary-dark {
  background-color: var(--primary-dark);
}

.bg-accent {
  background-color: var(--accent);
}

.bg-button-background {
  background-color: var(--button-background);
}

.bg-input-background {
  background-color: var(--input-background);
}

.text-text-primary {
  color: var(--text-primary);
}

.text-text-secondary {
  color: var(--text-secondary);
}

.text-primary {
  color: var(--primary);
}

.text-primary-dark {
  color: var(--primary-dark);
}

.text-accent {
  color: var(--accent);
}

.text-button-text {
  color: var(--button-text);
}

.text-input-text {
  color: var(--input-text);
}

.border-border {
  border-color: var(--border);
}

.border-primary {
  border-color: var(--primary);
}

.border-input-border {
  border-color: var(--input-border);
}

/* Hover variants */
.hover\:bg-primary:hover {
  background-color: var(--primary);
}

.hover\:bg-primary-dark:hover {
  background-color: var(--primary-dark);
}

.hover\:bg-background-secondary:hover {
  background-color: var(--background-secondary);
}

.hover\:bg-border:hover {
  background-color: var(--border);
}

.hover\:text-primary:hover {
  color: var(--primary);
}

.hover\:text-white:hover {
  color: #ffffff;
}

.hover\:border-primary\/20:hover {
  border-color: color-mix(in srgb, var(--primary) 20%, transparent);
}

/* Focus variants */
.focus\:ring-primary:focus {
  --tw-ring-color: var(--primary);
}

/* Gradient text utilities */
.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.from-primary {
  --tw-gradient-from: var(--primary);
  --tw-gradient-to: color-mix(in srgb, var(--primary) 0%, transparent);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.to-accent {
  --tw-gradient-to: var(--accent);
}

.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

.from-background {
  --tw-gradient-from: var(--background);
  --tw-gradient-to: color-mix(in srgb, var(--background) 0%, transparent);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.via-background-secondary {
  --tw-gradient-to: color-mix(in srgb, var(--background-secondary) 0%, transparent);
  --tw-gradient-stops: var(--tw-gradient-from), var(--background-secondary), var(--tw-gradient-to);
}

.to-background {
  --tw-gradient-to: var(--background);
}

/* Backdrop utilities */
.backdrop-blur-sm {
  backdrop-filter: blur(4px);
}

.backdrop-blur-md {
  backdrop-filter: blur(12px);
}

/* Additional utility classes */
.bg-clip-text {
  background-clip: text;
  -webkit-background-clip: text;
}

.text-transparent {
  color: transparent;
}

/* Shadow utilities */
.shadow-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.shadow-md {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.hover\:shadow-lg:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Transform utilities */
.hover\:-translate-y-1:hover {
  transform: translateY(-0.25rem);
}

.group-hover\:scale-110:hover {
  transform: scale(1.1);
}

/* Opacity utilities */
.bg-primary\/10 {
  background-color: color-mix(in srgb, var(--primary) 10%, transparent);
}

.bg-primary\/5 {
  background-color: color-mix(in srgb, var(--primary) 5%, transparent);
}

.bg-accent\/10 {
  background-color: color-mix(in srgb, var(--accent) 10%, transparent);
}

.bg-background\/80 {
  background-color: color-mix(in srgb, var(--background) 80%, transparent);
}

.bg-background\/95 {
  background-color: color-mix(in srgb, var(--background) 95%, transparent);
}

.border-primary\/20 {
  border-color: color-mix(in srgb, var(--primary) 20%, transparent);
}

.border-primary\/30 {
  border-color: color-mix(in srgb, var(--primary) 30%, transparent);
}

.hover\:border-primary\/20:hover {
  border-color: color-mix(in srgb, var(--primary) 20%, transparent);
}

.hover\:border-primary\/30:hover {
  border-color: color-mix(in srgb, var(--primary) 30%, transparent);
}

.group-hover\:bg-primary\/20:hover {
  background-color: color-mix(in srgb, var(--primary) 20%, transparent);
}

/* Additional responsive and state utilities */
.group:hover .group-hover\:scale-110 {
  transform: scale(1.1);
}

.group:hover .group-hover\:translate-x-1 {
  transform: translateX(0.25rem);
}

.group:hover .group-hover\:bg-primary\/20 {
  background-color: color-mix(in srgb, var(--primary) 20%, transparent);
}

/* Animation utilities */
.animate-bounce {
  animation: bounce 1s infinite;
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: none;
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Focus ring utilities */
.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-offset-2:focus {
  --tw-ring-offset-width: 2px;
}

/* Disabled state utilities */
.disabled\:opacity-50:disabled {
  opacity: 0.5;
}

.disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}
