'use client';

import { useEffect, useRef } from 'react';
import * as THREE from 'three';

const WaveAnimation = ({ className = '' }) => {
  const mountRef = useRef(null);
  const sceneRef = useRef(null);
  const rendererRef = useRef(null);
  const animationIdRef = useRef(null);

  useEffect(() => {
    const currentMount = mountRef.current;
    if (!currentMount) return;

    // Scene setup
    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    const renderer = new THREE.WebGLRenderer({ alpha: true, antialias: true });

    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setClearColor(0x000000, 0); // Transparent background
    currentMount.appendChild(renderer.domElement);

    // Store references
    sceneRef.current = scene;
    rendererRef.current = renderer;

    // Create wave geometry
    const geometry = new THREE.PlaneGeometry(20, 20, 100, 100);
    const material = new THREE.ShaderMaterial({
      uniforms: {
        time: { value: 0 },
        colorA: { value: new THREE.Color(0x0084ff) }, // Primary blue
        colorB: { value: new THREE.Color(0x00baff) }, // Accent blue
        opacity: { value: 0.3 }
      },
      vertexShader: `
        uniform float time;
        varying vec2 vUv;
        varying float vWave;

        void main() {
          vUv = uv;
          
          vec3 pos = position;
          float wave1 = sin(pos.x * 0.5 + time * 0.8) * 0.5;
          float wave2 = sin(pos.y * 0.3 + time * 0.6) * 0.3;
          float wave3 = sin((pos.x + pos.y) * 0.2 + time * 1.2) * 0.2;
          
          pos.z = wave1 + wave2 + wave3;
          vWave = pos.z;
          
          gl_Position = projectionMatrix * modelViewMatrix * vec4(pos, 1.0);
        }
      `,
      fragmentShader: `
        uniform vec3 colorA;
        uniform vec3 colorB;
        uniform float opacity;
        varying vec2 vUv;
        varying float vWave;

        void main() {
          float mixFactor = (vWave + 1.0) * 0.5;
          vec3 color = mix(colorA, colorB, mixFactor);
          
          float alpha = opacity * (0.5 + 0.5 * sin(vWave * 2.0));
          gl_FragColor = vec4(color, alpha);
        }
      `,
      transparent: true,
      side: THREE.DoubleSide
    });

    const mesh = new THREE.Mesh(geometry, material);
    mesh.rotation.x = -Math.PI / 3;
    mesh.position.z = -5;
    scene.add(mesh);

    // Camera position
    camera.position.z = 5;

    // Animation loop
    const animate = () => {
      animationIdRef.current = requestAnimationFrame(animate);
      
      material.uniforms.time.value += 0.01;
      mesh.rotation.z += 0.001;
      
      renderer.render(scene, camera);
    };

    animate();

    // Handle resize
    const handleResize = () => {
      if (!currentMount) return;

      const width = currentMount.clientWidth;
      const height = currentMount.clientHeight;
      
      camera.aspect = width / height;
      camera.updateProjectionMatrix();
      renderer.setSize(width, height);
    };

    window.addEventListener('resize', handleResize);
    handleResize();

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);
      
      if (animationIdRef.current) {
        cancelAnimationFrame(animationIdRef.current);
      }
      
      if (currentMount && renderer.domElement) {
        currentMount.removeChild(renderer.domElement);
      }
      
      // Dispose of Three.js objects
      geometry.dispose();
      material.dispose();
      renderer.dispose();
    };
  }, []);

  return (
    <div 
      ref={mountRef} 
      className={`absolute inset-0 pointer-events-none ${className}`}
      style={{ zIndex: 1 }}
    />
  );
};

export default WaveAnimation;
